import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { rawInstance } from '@/libs/request/instance'

export const AuthedImg: React.FC<React.ImgHTMLAttributes<HTMLImageElement>> = ({ src, ...props }) => {
  const { data: parsedUrl } = useQuery({
    queryKey: ['AUTH_IMAGE', src],
    queryFn: async () => {
      if (!src) return null
      const aTag = await rawInstance.get(src).then(r => r.data)
      const doc = new DOMParser().parseFromString(aTag, 'text/html')
      return doc.querySelector('a')?.href || null
    },
    staleTime: 3600_000
  })

  return (
    <img
      src={parsedUrl || ''}
      {...props}
    />
  )
}
