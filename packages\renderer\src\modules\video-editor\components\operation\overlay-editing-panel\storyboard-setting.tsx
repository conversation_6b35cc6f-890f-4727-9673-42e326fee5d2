import React, { useMemo } from 'react'
import { useEditorContext, useOverlayEditing } from '@/modules/video-editor/contexts'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import { useQueryScript } from '@/hooks/queries/useQueryScript'
import { TrackType } from '@/modules/video-editor/types'

export const StoryboardSetting: React.FC = () => {
  const { scriptId, tracks } = useEditorContext()
  const { data: script } = useQueryScript(scriptId)

  const { localOverlay } = useOverlayEditing<TextOverlay>()

  const index = useMemo(() => {
    return tracks
      .find(t => t.type === TrackType.STORYBOARD)
      ?.overlays
      .findIndex(o => o.id === localOverlay.id)
      ?? -1
  }, [tracks, localOverlay])

  if (index === -1) return null

  return (
    <div className="flex flex-col gap-3">

      <div className="grid grid-cols-2 text-sm gap-4 text-gray-400">
        <span>分镜号</span>
        <div>#{index + 1}</div>

        <span>分镜名称</span>
        <div>{script?.scenes[index]?.title}</div>

        <span>分镜时长</span>
      </div>
    </div>
  )
}

