import { useSidebar } from '@/modules/video-editor/contexts'
import { getResourcePlugin } from '@/modules/video-editor/resource-plugin-system'
import * as React from 'react'
import { PageLoading } from '@/components/LoadingIndicator'

export function MaterialsContent() {
  const { activePanel } = useSidebar()

  const renderActivePanel = () => {
    const PluginComponent = getResourcePlugin(activePanel)?.component
    if (!PluginComponent) return null

    return (
      <React.Suspense fallback={<PageLoading />}>
        <PluginComponent />
      </React.Suspense>
    )
  }

  return (
    <div
      className="h-full w-full items-center justify-center hidden flex-1 md:flex border-r overflow-y-auto overflow-x-hidden"
    >
      {renderActivePanel()}
    </div>
  )
}
