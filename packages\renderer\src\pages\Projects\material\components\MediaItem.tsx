import React, { useCallback, useRef, useState } from 'react'
import { Ellipsis, EllipsisVertical, FolderInput, Music } from 'lucide-react'
import { MaterialResource } from '@/types/resources'
import folderIcon from '@/assets/folder.svg'
import { cn } from '@/components/lib/utils'
import { createPortal } from 'react-dom'
import { AuthedImg } from '@/components/authed-img'

interface MediaItemProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation: string
  media: MaterialResource.Media
  isSelected: boolean
  isFolder: boolean
  actions: FolderAction[] | MediaAction[]
  currentFolderId?: string
  isTrash?: boolean
  isEditItem?: boolean
  draggable?: boolean
  onToggleSelect: (fileId: string) => void
  onFolderClick?: () => void
  onItemAdd?: () => Promise<void> | void
  onbatchImportStoryboards?: () => Promise<void> | void
}

type FolderActionHandler = (nodeId: string, parentId?: string, label?: string) => void
export type FolderAction = {
  icon: React.ReactNode
  label: string
  onClick: FolderActionHandler
}

type MediaActionHandler = (fileId: string, fileName: string, folderUuid: string) => void
export type MediaAction = {
  icon: React.ReactNode
  label: string
  onClick: MediaActionHandler
}

const MediaItemComponent: React.FC<MediaItemProps> = ({
  orientation,
  media,
  isSelected,
  isFolder,
  actions,
  currentFolderId,
  isTrash = false,
  isEditItem = false,
  draggable,
  onToggleSelect,
  onFolderClick,
  onItemAdd,
  onbatchImportStoryboards,
  ...rest
}) => {
  const [popup, setPopup] = useState(false)
  const [editPopup, setEditPopup] = useState(false)
  const ellipsisRef = useRef<HTMLDivElement>(null)
  const editEllipsisRef = useRef<HTMLSpanElement>(null)

  // 使用 useQuery 获取封面URL
  // const { data: coverUrl = '#' } = useQueryMediaCover(
  //   media.cover,
  //   !isFolder && !!media.cover // 只有在非文件夹且有封面时才启用查询
  // )

  const renderActionsMenu = useCallback(
    (
      anchorRef: React.RefObject<HTMLElement | null>,
      actions: (FolderAction | MediaAction)[],
      isFolder: boolean,
      media: MaterialResource.Media,
      currentFolderId?: string,
      onClose?: () => void,
    ) => {
      const rect = anchorRef.current?.getBoundingClientRect()
      if (!rect) return null

      return createPortal(
        <div
          style={{
            position: 'absolute',
            top: rect.bottom + window.scrollY,
            left: rect.left + window.scrollX,
            zIndex: 9999,
          }}
          onMouseLeave={onClose}
        >
          <div className="border dark:bg-neutral-800 rounded shadow-lg min-w-[150px] py-2 px-4">
            {actions
              .filter(action => action.label !== '新建文件夹')
              .map((action, idx) => (
                <div
                  key={idx}
                  className="flex items-center px-3 py-1 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700"
                  onClick={e => {
                    e.stopPropagation()
                    if (isFolder) {
                      ;(action as FolderAction).onClick?.(media.fileId, currentFolderId, media.fileName)
                    } else {
                      ;(action as MediaAction).onClick?.(media.fileId, media.fileName, media.folderUuid)
                    }
                    onClose?.()
                  }}
                >
                  {action.icon}
                  <span className="ml-2">{action.label}</span>
                </div>
              ))}
          </div>
        </div>,
        document.body,
      )
    },
    []
  )

  return (
    <div
      key={media.fileId}
      draggable={draggable}
      {...rest}
      className={cn(
        'flex flex-col relative group',
        !isEditItem ? (orientation === 'horizontal' ? 'w-50' : 'w-40') : '',
      )}
      onClick={e => {
        const target = e.target as Element

        // 判断点击是否来自复选框
        if (!target.closest('input[type="checkbox"]')) {
          if (isFolder && onFolderClick) {
            onFolderClick() // 确保调用函数
          }
        }
      }}
    >
      {/* 类型图标和选择框 */}
      <div
        className={cn(
          'w-full border rounded-sm shadow-sm flex flex-col relative group',
          !isEditItem ? (orientation === 'horizontal' ? 'h-50' : 'h-64') : '',
        )}
      >
        {/* 回收站特有信息显示 */}
        {isTrash && (
          <div className="absolute top-2 left-2 text-xs text-gray-500 bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
            剩余xx天
          </div>
        )}

        {!isFolder && !isTrash && (
          <div
            className={cn(
              'absolute text-gray-500 bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block',
              isEditItem ? 'text-[0.5rem] top-0 left-0' : 'text-xs top-2 left-2',
            )}
          >
            {media.resType !== MaterialResource.MediaType.AUDIO && `合成使用${media.useCount}次`}
          </div>
        )}

        <div className={cn('absolute  z-3', isEditItem ? 'top-0 right-1' : 'top-2 right-2')}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={e => {
              e.stopPropagation()
              onToggleSelect(media.fileId)
            }}
            className="w-4 h-4 cursor-pointer accent-primary-highlight1"
          />
        </div>
        <div
          className={cn(
            'flex items-center justify-center relative',
            !isEditItem ? (orientation === 'horizontal' ? 'h-50' : 'h-64') : 'h-35',
          )}
        >
          <div className="flex items-center justify-center space-x-1 overflow-hidden">
            {media.resType === 0 && <img src={folderIcon} alt="文件夹" className="w-[40%] h-[40%]" />}
            {media.resType === MaterialResource.MediaType.VIDEO && (
              <AuthedImg src={media.cover} alt={media.fileName} className="w-full h-auto object-cover max-w-full rounded" />
            )}
            {media.resType === MaterialResource.MediaType.AUDIO && <Music className="w-[40%] h-[40%]" />}
            {media.resType === MaterialResource.MediaType.IMAGE && (
              <AuthedImg src={media.cover} alt={media.fileName} className="w-full h-auto object-cover max-w-full" />
            )}
          </div>
          <div className="absolute bottom-1 left-2 text-xs text-gray-500">
            {media.resType === 0 ? (
              isEditItem ? (
                <div className="text-[0.5rem] bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                  文件夹 {media.childrenFolder}，素材 {media.mediaNum}
                </div>
              ) : (
                <div className="text-xs bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                  {media.childrenFolder}个文件夹，{media.mediaNum}个素材
                </div>
              )
            ) : isEditItem ? (
              media.resType === MaterialResource.MediaType.IMAGE ? null : (
                <div className="text-xs bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">{media.duration}</div>
              )
            ) : (
              <div className="text-xs bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                {media.resType === MaterialResource.MediaType.IMAGE
                  ? `${media.fileSize}MB`
                  : `${media.duration}/${media.fileSize}MB`}
              </div>
            )}
          </div>
          {isEditItem && media.resType === 0 && (
            <div
              className={cn(
                'absolute bottom-1 inset-x-1 rounded py-1 flex text-black items-center justify-evenly bg-blue-200 transition-opacity',
                editPopup ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
              )}
            >
              <FolderInput
                className="w-4 h-4"
                onClick={e => {
                  e.stopPropagation() // 防止触发父层点击
                  onbatchImportStoryboards?.()
                }}
              >
                {' '}
                <title>批量导入到分镜</title>
              </FolderInput>
              <div className="w-1px h-4 bg-black" />

              <span
                className=""
                ref={editEllipsisRef}
                onClick={e => {
                  e.stopPropagation()
                }}
                onMouseEnter={() => setEditPopup(true)}
                onMouseLeave={() => setEditPopup(false)}
              >
                <Ellipsis className="w-4 h-4" />
              </span>
              {editPopup &&
                renderActionsMenu(editEllipsisRef, actions, isFolder, media, currentFolderId, () =>
                  setEditPopup(false),
                )}
            </div>
          )}
          {isEditItem && media.resType !== 0 && (
            <span
              className="absolute right-1 bottom-1"
              ref={editEllipsisRef}
              onClick={e => {
                e.stopPropagation()
              }}
              onMouseEnter={() => setEditPopup(true)}
              onMouseLeave={() => setEditPopup(false)}
            >
              <Ellipsis className="w-4 h-4" />
            </span>
          )}
          {editPopup &&
            renderActionsMenu(editEllipsisRef, actions, isFolder, media, currentFolderId, () => setEditPopup(false))}
        </div>
      </div>

      {/* 信息 */}
      <div className="flex-1 flex flex-col justify-end py-2">
        <div className="text-sm font-medium truncate mb-1">{media.fileName}</div>
        <div className="text-xs text-gray-400">
          {media.createTime ? new Date(media.createTime).toLocaleTimeString() : ''}
        </div>
      </div>
      {/* 右下角更多操作 */}
      {!isEditItem && (
        <div className="absolute bottom-2 right-0 text-sm">
          <button className="" onMouseEnter={() => setPopup(true)} onMouseLeave={() => setPopup(false)}>
            <div ref={ellipsisRef} className="hover:bg-gray-100 dark:hover:bg-neutral-700 rounded p-1">
              <EllipsisVertical className="w-6 h-6 text-gray-500" />
            </div>
            {popup &&
              renderActionsMenu(
                ellipsisRef as React.RefObject<HTMLElement | null>,
                actions,
                isFolder,
                media,
                currentFolderId,
                () => setPopup(false),
              )}
          </button>
        </div>
      )}
    </div>
  )
}

const MediaItem = React.memo(MediaItemComponent)

export default MediaItem
